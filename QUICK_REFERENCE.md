# Python原型链污染快速参考

## 基础概念
- 通过merge函数污染类属性
- 利用__globals__访问全局变量空间
- 通过继承关系(__class__.__base__)污染父类

## 常用访问路径
- __init__.__globals__                    # 访问全局变量
- __class__.__base__                      # 访问父类
- __loader__.__init__.__globals__['sys']  # 通过loader访问sys
- __spec__.__init__.__globals__['sys']    # 通过spec访问sys

## CTF常用目标
### Flask应用
- app.config.SECRET_KEY                   # Flask密钥
- app._got_first_request                  # 首次请求标志
- app._static_url_path                    # 静态文件路径
- app.jinja_env.variable_start_string     # Jinja语法标识符

### 环境变量
- os.environ.LD_PRELOAD                   # 动态库劫持
- os.environ.PATH                         # 路径劫持
- os.environ.PYTHONPATH                   # Python模块路径

### 函数参数
- function.__defaults__                   # 位置参数默认值
- function.__kwdefaults__                 # 关键字参数默认值

## Payload格式
### 标准merge格式
```json
{
    "__init__": {
        "__globals__": {
            "target_var": "polluted_value"
        }
    }
}
```

### Pydash格式
```
__init__.__globals__.target_var
```

## 注意事项
1. Object类无法被污染
2. __defaults__必须是元组类型
3. 模板缓存可能影响Jinja污染效果
4. 某些属性有类型限制
