class NoDataDesc:
    def __get__(self, instance, owner):
        return "from Data Desc"

class DataDesc:
    def __get__(self, instance, owner):
        return "from NoData Desc"

    def __set__(self, instance, value):
        pass

class Test:
    data_desc = DataDesc()
    no_data_desc = NoDataDesc()

test = Test()
test.data_desc = "set data desc"
test.no_data_desc = "set no data desc"

print(f"数据描述符优先级最高，无法覆盖{test.data_desc}, 非数据描述符优先级低，可以覆盖{test.no_data_desc}")