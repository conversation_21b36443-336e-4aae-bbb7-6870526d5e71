#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局变量污染示例
通过__globals__属性污染全局变量和无继承关系的类
"""

import sys

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

# 全局变量
secret_var = 114
admin_flag = False

def test_func():
    pass

class TargetClass:
    secret_attr = "original"
    config = {"enabled": False}

class UnrelatedClass:
    def __init__(self):
        pass

print("=== 全局变量污染 ===")
print(f"污染前 secret_var: {secret_var}")
print(f"污染前 admin_flag: {admin_flag}")
print(f"污染前 TargetClass.secret_attr: {TargetClass.secret_attr}")

instance = UnrelatedClass()

# Payload: 通过__init__.__globals__访问全局变量
payload = {
    "__init__": {
        "__globals__": {
            "secret_var": 514,
            "admin_flag": True,
            "TargetClass": {
                "secret_attr": "polluted",
                "config": {"enabled": True, "admin": True}
            }
        }
    }
}

merge(payload, instance)
print(f"污染后 secret_var: {secret_var}")
print(f"污染后 admin_flag: {admin_flag}")
print(f"污染后 TargetClass.secret_attr: {TargetClass.secret_attr}")
print(f"污染后 TargetClass.config: {TargetClass.config}")

# 示例2: 通过函数__globals__污染
print("\n=== 通过函数__globals__污染 ===")
another_secret = "function_secret"

class AnotherTarget:
    value = "original_value"

class FunctionPolluter:
    def method(self):
        pass

polluter = FunctionPolluter()
print(f"污染前 another_secret: {another_secret}")
print(f"污染前 AnotherTarget.value: {AnotherTarget.value}")

# Payload: 通过方法的__globals__
payload2 = {
    "method": {
        "__globals__": {
            "another_secret": "polluted_function_secret",
            "AnotherTarget": {
                "value": "polluted_value"
            }
        }
    }
}

merge(payload2, polluter)
print(f"污染后 another_secret: {another_secret}")
print(f"污染后 AnotherTarget.value: {AnotherTarget.value}")

# 验证__globals__指向同一个全局空间
print(f"\n验证: test_func.__globals__ == globals(): {test_func.__globals__ == globals()}")
print(f"验证: UnrelatedClass.__init__.__globals__ == globals(): {UnrelatedClass.__init__.__globals__ == globals()}")
