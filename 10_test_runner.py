#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 运行所有示例并验证污染效果
"""

import sys
import os
import importlib.util

def run_example(filename):
    """运行单个示例文件"""
    print(f"\n{'='*60}")
    print(f"运行示例: {filename}")
    print('='*60)
    
    try:
        # 动态导入模块
        spec = importlib.util.spec_from_file_location("example", filename)
        if spec is None:
            print(f"无法加载 {filename}")
            return False
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✓ {filename} 运行成功")
        return True
        
    except Exception as e:
        print(f"✗ {filename} 运行失败: {e}")
        return False

def main():
    """主函数"""
    print("Python原型链污染学习用例测试运行器")
    print("="*60)
    
    # 示例文件列表
    examples = [
        "01_basic_pollution.py",
        "02_global_pollution.py", 
        "03_module_pollution.py",
        "04_function_defaults.py",
        "07_environment_pollution.py",
        "08_pydash_example.py",
        "09_ctf_payloads.py"
    ]
    
    # Flask示例需要特殊处理，不在自动测试中运行
    flask_examples = [
        "05_flask_basic.py",
        "06_flask_jinja.py"
    ]
    
    success_count = 0
    total_count = len(examples)
    
    # 运行基础示例
    for example in examples:
        if os.path.exists(example):
            if run_example(example):
                success_count += 1
        else:
            print(f"文件不存在: {example}")
    
    # 显示Flask示例信息
    print(f"\n{'='*60}")
    print("Flask示例 (需要手动运行):")
    print('='*60)
    for example in flask_examples:
        if os.path.exists(example):
            print(f"✓ {example} - 运行: python {example}")
        else:
            print(f"✗ {example} - 文件不存在")
    
    # 显示结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print('='*60)
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有基础示例运行成功!")
    else:
        print("⚠️  部分示例运行失败，请检查错误信息")
    
    # 显示使用说明
    print(f"\n{'='*60}")
    print("使用说明")
    print('='*60)
    print("1. 基础学习:")
    print("   python 01_basic_pollution.py      # 基础原型链污染")
    print("   python 02_global_pollution.py     # 全局变量污染")
    print("   python 03_module_pollution.py     # 模块污染")
    
    print("\n2. 高级技巧:")
    print("   python 04_function_defaults.py    # 函数默认参数污染")
    print("   python 07_environment_pollution.py # 环境变量污染")
    print("   python 08_pydash_example.py       # Pydash模块污染")
    
    print("\n3. Flask应用 (需要安装Flask):")
    print("   python 05_flask_basic.py          # Flask基础污染")
    print("   python 06_flask_jinja.py          # Jinja模板污染")
    
    print("\n4. CTF工具:")
    print("   python 09_ctf_payloads.py         # CTF常用payload集合")
    
    print("\n5. 依赖安装:")
    print("   pip install flask pydash          # 安装可选依赖")
    
    # 创建快速参考
    create_quick_reference()

def create_quick_reference():
    """创建快速参考文件"""
    reference_content = """# Python原型链污染快速参考

## 基础概念
- 通过merge函数污染类属性
- 利用__globals__访问全局变量空间
- 通过继承关系(__class__.__base__)污染父类

## 常用访问路径
- __init__.__globals__                    # 访问全局变量
- __class__.__base__                      # 访问父类
- __loader__.__init__.__globals__['sys']  # 通过loader访问sys
- __spec__.__init__.__globals__['sys']    # 通过spec访问sys

## CTF常用目标
### Flask应用
- app.config.SECRET_KEY                   # Flask密钥
- app._got_first_request                  # 首次请求标志
- app._static_url_path                    # 静态文件路径
- app.jinja_env.variable_start_string     # Jinja语法标识符

### 环境变量
- os.environ.LD_PRELOAD                   # 动态库劫持
- os.environ.PATH                         # 路径劫持
- os.environ.PYTHONPATH                   # Python模块路径

### 函数参数
- function.__defaults__                   # 位置参数默认值
- function.__kwdefaults__                 # 关键字参数默认值

## Payload格式
### 标准merge格式
```json
{
    "__init__": {
        "__globals__": {
            "target_var": "polluted_value"
        }
    }
}
```

### Pydash格式
```
__init__.__globals__.target_var
```

## 注意事项
1. Object类无法被污染
2. __defaults__必须是元组类型
3. 模板缓存可能影响Jinja污染效果
4. 某些属性有类型限制
"""
    
    with open("QUICK_REFERENCE.md", "w", encoding="utf-8") as f:
        f.write(reference_content)
    
    print(f"\n✓ 已创建快速参考文件: QUICK_REFERENCE.md")

if __name__ == "__main__":
    main()
